"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d5af36c783ee\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJEOlxcaGFja290aG9uXFxCbG9ja2NoYWluXFxWaW50cmVrXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkNWFmMzZjNzgzZWVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/providers/ClientWalletProvider.tsx":
/*!***********************************************************!*\
  !*** ./src/components/providers/ClientWalletProvider.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientWalletProvider: () => (/* binding */ ClientWalletProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _WalletProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./WalletProvider */ \"(app-pages-browser)/./src/components/providers/WalletProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ ClientWalletProvider auto */ \nvar _s = $RefreshSig$();\n\n\n// Default wallet context for SSR\nconst defaultWalletContext = {\n    wallet: null,\n    connected: false,\n    connecting: false,\n    address: null,\n    balance: null,\n    connect: async ()=>{},\n    disconnect: ()=>{},\n    getAvailableWallets: ()=>[]\n};\nconst WalletContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(defaultWalletContext);\nfunction ClientWalletProvider(param) {\n    let { children } = param;\n    _s();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClientWalletProvider.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"ClientWalletProvider.useEffect\"], []);\n    if (!mounted) {\n        // Provide default context during SSR\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletContext.Provider, {\n            value: defaultWalletContext,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\providers\\\\ClientWalletProvider.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WalletProvider__WEBPACK_IMPORTED_MODULE_2__.WalletProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\providers\\\\ClientWalletProvider.tsx\",\n        lineNumber: 40,\n        columnNumber: 10\n    }, this);\n}\n_s(ClientWalletProvider, \"LrrVfNW3d1raFE0BNzCTILYmIfo=\");\n_c = ClientWalletProvider;\nvar _c;\n$RefreshReg$(_c, \"ClientWalletProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers/ClientWalletProvider.tsx\n"));

/***/ })

});